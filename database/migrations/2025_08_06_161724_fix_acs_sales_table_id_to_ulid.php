<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop foreign key constraint first
        DB::statement('ALTER TABLE acs_commission_distributions DROP FOREIGN KEY acs_commission_distributions_acs_sales_id_foreign');

        Schema::table('acs_sales', function (Blueprint $table) {
            // Drop the existing id column
            $table->dropColumn('id');
        });

        Schema::table('acs_sales', function (Blueprint $table) {
            // Add ULID column
            $table->ulid('id')->primary();
        });

        // Recreate the foreign key constraint with the new ULID type
        DB::statement('ALTER TABLE acs_commission_distributions MODIFY acs_sales_id char(26) NOT NULL');
        DB::statement('ALTER TABLE acs_commission_distributions ADD CONSTRAINT acs_commission_distributions_acs_sales_id_foreign FOREIGN KEY (acs_sales_id) REFERENCES acs_sales(id) ON DELETE CASCADE');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if foreign key constraint exists before dropping
        $constraints = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.TABLE_CONSTRAINTS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'acs_commission_distributions'
            AND CONSTRAINT_TYPE = 'FOREIGN KEY'
            AND CONSTRAINT_NAME = 'acs_commission_distributions_acs_sales_id_foreign'
        ");

        if (!empty($constraints)) {
            // Drop foreign key constraint first
            DB::statement('ALTER TABLE acs_commission_distributions DROP FOREIGN KEY acs_commission_distributions_acs_sales_id_foreign');
        }

        Schema::table('acs_sales', function (Blueprint $table) {
            // Drop the ULID column
            $table->dropColumn('id');
        });

        Schema::table('acs_sales', function (Blueprint $table) {
            // Add back the auto-incrementing id column
            $table->id();
        });

        // Recreate the foreign key constraint with the original bigint type
        DB::statement('ALTER TABLE acs_commission_distributions MODIFY acs_sales_id bigint(20) unsigned NOT NULL');
        DB::statement('ALTER TABLE acs_commission_distributions ADD CONSTRAINT acs_commission_distributions_acs_sales_id_foreign FOREIGN KEY (acs_sales_id) REFERENCES acs_sales(id) ON DELETE CASCADE');
    }
};
