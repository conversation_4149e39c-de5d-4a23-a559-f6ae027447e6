<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if acs_sales table has ULID id column
        $hasUlidId = false;
        try {
            $columns = DB::select("SHOW COLUMNS FROM acs_sales LIKE 'id'");
            if (!empty($columns)) {
                $column = $columns[0];
                $hasUlidId = str_contains($column->Type, 'char(26)');
            }
        } catch (\Exception $e) {
            // Table might not exist yet, assume it will be created with ULID
            $hasUlidId = true;
        }

        // Check if foreign key constraints already exist
        $constraints = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.TABLE_CONSTRAINTS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'acs_commission_distributions'
            AND CONSTRAINT_TYPE = 'FOREIGN KEY'
        ");
        $existingConstraints = array_column($constraints, 'CONSTRAINT_NAME');

        if ($hasUlidId) {
            // Change acs_sales_id column type from bigint to char(26) to match ULID
            if (!in_array('acs_commission_distributions_acs_sales_id_foreign', $existingConstraints)) {
                DB::statement('ALTER TABLE acs_commission_distributions MODIFY acs_sales_id char(26) NOT NULL');
                DB::statement('ALTER TABLE acs_commission_distributions ADD CONSTRAINT acs_commission_distributions_acs_sales_id_foreign FOREIGN KEY (acs_sales_id) REFERENCES acs_sales(id) ON DELETE CASCADE');
            }
        } else {
            // Keep acs_sales_id as bigint if acs_sales still has bigint id
            if (!in_array('acs_commission_distributions_acs_sales_id_foreign', $existingConstraints)) {
                DB::statement('ALTER TABLE acs_commission_distributions MODIFY acs_sales_id bigint(20) unsigned NOT NULL');
                DB::statement('ALTER TABLE acs_commission_distributions ADD CONSTRAINT acs_commission_distributions_acs_sales_id_foreign FOREIGN KEY (acs_sales_id) REFERENCES acs_sales(id) ON DELETE CASCADE');
            }
        }

        // Add other foreign key constraints only if they don't exist
        if (!in_array('acs_commission_distributions_acs_coorperative_id_foreign', $existingConstraints)) {
            DB::statement('ALTER TABLE acs_commission_distributions ADD CONSTRAINT acs_commission_distributions_acs_coorperative_id_foreign FOREIGN KEY (acs_coorperative_id) REFERENCES acs_coorperative(id)');
        }
        if (!in_array('acs_commission_distributions_acs_coorperative_branch_id_foreign', $existingConstraints)) {
            DB::statement('ALTER TABLE acs_commission_distributions ADD CONSTRAINT acs_commission_distributions_acs_coorperative_branch_id_foreign FOREIGN KEY (acs_coorperative_branch_id) REFERENCES acs_coorperative_branch(id)');
        }
        if (!in_array('acs_commission_distributions_acs_main_agent_user_id_foreign', $existingConstraints)) {
            DB::statement('ALTER TABLE acs_commission_distributions ADD CONSTRAINT acs_commission_distributions_acs_main_agent_user_id_foreign FOREIGN KEY (acs_main_agent_user_id) REFERENCES acs_users(id)');
        }
        if (!in_array('acs_commission_distributions_acs_agent_user_id_foreign', $existingConstraints)) {
            DB::statement('ALTER TABLE acs_commission_distributions ADD CONSTRAINT acs_commission_distributions_acs_agent_user_id_foreign FOREIGN KEY (acs_agent_user_id) REFERENCES acs_users(id)');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints
        DB::statement('ALTER TABLE acs_commission_distributions DROP FOREIGN KEY acs_commission_distributions_acs_sales_id_foreign');
        DB::statement('ALTER TABLE acs_commission_distributions DROP FOREIGN KEY acs_commission_distributions_acs_coorperative_id_foreign');
        DB::statement('ALTER TABLE acs_commission_distributions DROP FOREIGN KEY acs_commission_distributions_acs_coorperative_branch_id_foreign');
        DB::statement('ALTER TABLE acs_commission_distributions DROP FOREIGN KEY acs_commission_distributions_acs_main_agent_user_id_foreign');
        DB::statement('ALTER TABLE acs_commission_distributions DROP FOREIGN KEY acs_commission_distributions_acs_agent_user_id_foreign');

        // Revert acs_sales_id column type back to bigint
        DB::statement('ALTER TABLE acs_commission_distributions MODIFY acs_sales_id bigint(20) unsigned NOT NULL');
    }
};
