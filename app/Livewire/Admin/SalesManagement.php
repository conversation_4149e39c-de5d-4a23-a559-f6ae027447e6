<?php

namespace App\Livewire\Admin;

use Livewire\Component;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\WithPagination;
use App\Models\AcsSales;
use App\Models\AcsUser;
use App\Models\AcsCooperative;
use Illuminate\Support\Facades\DB;

#[Layout('layouts.account.app')]
#[Title('Sales Management')]
class SalesManagement extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    // Filter properties
    public $search = '';
    public $selectedPurity = '';
    public $selectedCooperative = '';
    public $selectedAgent = '';
    public $perPage = 10;
    public $showFilters = false;

    // Modal properties
    public $showModal = false;
    public $selectedSale = null;

    // Data for dropdowns
    public $cooperatives = [];
    public $agents = [];

    public function mount()
    {
        // Load cooperatives for the dropdown
        $this->cooperatives = AcsCooperative::orderBy('name')
            ->pluck('name', 'id')
            ->toArray();

        // Load agents for the dropdown
        $this->agents = AcsUser::whereHas('role', function($query) {
                $query->where('name', 'agent');
            })
            ->orderBy('name')
            ->pluck('name', 'id')
            ->toArray();
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingSelectedPurity()
    {
        $this->resetPage();
    }

    public function updatingSelectedCooperative()
    {
        $this->resetPage();
    }

    public function updatingSelectedAgent()
    {
        $this->resetPage();
    }

    public function updatingPerPage()
    {
        $this->resetPage();
    }

    public function toggleFilters()
    {
        $this->showFilters = !$this->showFilters;
    }

    public function clearFilters()
    {
        $this->search = '';
        $this->selectedPurity = '';
        $this->selectedCooperative = '';
        $this->selectedAgent = '';
        $this->resetPage();
    }

    public function showSaleDetails($saleId)
    {
        $this->selectedSale = DB::table('acs_sales')
            ->leftJoin('acs_users', 'acs_sales.acs_users_id', '=', 'acs_users.id')
            ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
            ->leftJoin('acs_coorperative_branch', 'acs_users.acs_coorperative_branch_id', '=', 'acs_coorperative_branch.id')
            ->leftJoin('acs_roles', 'acs_users.acs_role_id', '=', 'acs_roles.id')
            ->leftJoin('acs_comission_settings', 'acs_sales.variety_type', '=', 'acs_comission_settings.variety_type')
            ->select(
                'acs_sales.*',
                'acs_users.name as user_name',
                'acs_users.email as user_email',
                'acs_coorperative.name as cooperative_name',
                'acs_coorperative_branch.name as branch_name',
                'acs_roles.name as role_name',
                'acs_comission_settings.variety_name'
            )
            ->where('acs_sales.id', $saleId)
            ->first();

        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->selectedSale = null;
    }

    public function getSalesSummary()
    {
        return [
            'total_sales' => AcsSales::count(),
            'total_value' => AcsSales::sum('total_price'),
            'avg_sale_value' => AcsSales::avg('total_price'),
        ];
    }

    public function render()
    {
        $sales = DB::table('acs_sales')
            ->leftJoin('acs_users', 'acs_sales.acs_users_id', '=', 'acs_users.id')
            ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
            ->leftJoin('acs_coorperative_branch', 'acs_users.acs_coorperative_branch_id', '=', 'acs_coorperative_branch.id')
            ->leftJoin('acs_roles', 'acs_users.acs_role_id', '=', 'acs_roles.id')
            ->leftJoin('acs_comission_settings', 'acs_sales.variety_type', '=', 'acs_comission_settings.variety_type')
            ->select(
                'acs_sales.*',
                'acs_users.name as agent_name',
                'acs_users.email as agent_email',
                'acs_coorperative.name as cooperative_name',
                'acs_coorperative_branch.name as branch_name',
                'acs_roles.name as role_name',
                'acs_comission_settings.variety_name'
            )
            ->whereNull('acs_sales.deleted_at')
            ->when($this->search, function($query) {
                $query->where(function($q) {
                    $q->where('acs_sales.sku_number', 'like', '%' . $this->search . '%')
                      ->orWhere('acs_sales.variety_type', 'like', '%' . $this->search . '%')
                      ->orWhere('acs_comission_settings.variety_name', 'like', '%' . $this->search . '%')
                      ->orWhere('acs_sales.product_category_code', 'like', '%' . $this->search . '%')
                      ->orWhere('acs_users.name', 'like', '%' . $this->search . '%')
                      ->orWhere('acs_coorperative.name', 'like', '%' . $this->search . '%')
                      ->orWhere('acs_coorperative_branch.name', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->selectedCooperative, function($query) {
                $query->where('acs_users.acs_coorperative_id', $this->selectedCooperative);
            })
            ->when($this->selectedAgent, function($query) {
                $query->where('acs_sales.acs_users_id', $this->selectedAgent);
            })
            ->orderBy('acs_sales.created_at', 'desc')
            ->paginate($this->perPage);

        return view('livewire.admin.sales-management', [
            'sales' => $sales,
            'salesSummary' => $this->getSalesSummary()
        ]);
    }
}
