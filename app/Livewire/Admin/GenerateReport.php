<?php

namespace App\Livewire\Admin;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\WithPagination;
use App\Models\AcsSalesCommission;
use App\Models\AcsCommissionDistribution;

class GenerateReport extends Component
{
    use WithPagination;

    public $isSubmitted = false;
    public $activeTab = 'sales'; // 'sales' or 'history'
    public $searchTerm = '';
    public $dateTo = '';
    public $processedDateTo = ''; // For filtering processed sales by date

    // New properties for popup modal
    public $showGenerateModal = false;
    public $lastCalculateDate = '';
    public $previewTotalMainAgent = 0;
    public $previewTotalAgent = 0;
    public $cooperativeBreakdown = [];

    protected $paginationTheme = 'bootstrap';

    public function mount()
    {
        // Check if there are any unprocessed sales (status = 0)
        // If no unprocessed sales exist, consider it submitted
        $this->isSubmitted = !DB::table('acs_sales')->where('status', 0)->exists();
    }

    public function render()
    {
        return view('livewire.admin.generate-report');
    }

    public function generate(){
        // Check if there are any unprocessed sales to process
        $unprocessedSalesCount = DB::table('acs_sales')->where('status', 0)->count();

        if ($unprocessedSalesCount === 0) {
            session()->flash('error', 'No unprocessed sales found. All sales have already been processed.');
            return;
        }

        // Set default last calculate date to today
        $this->lastCalculateDate = now()->format('Y-m-d');

        // Calculate preview totals
        $this->calculatePreviewTotals();

        // Show the modal
        $this->showGenerateModal = true;
    }

        public function calculatePreviewTotals()
    {
        // Reset values
        $this->previewTotalMainAgent = 0;
        $this->previewTotalAgent = 0;
        $this->cooperativeBreakdown = [];

        if (!$this->lastCalculateDate) {
            return;
        }

        // Get aggregated sales data grouped by user (only status = 0 and up to selected date)
        $userSales = DB::table('acs_sales')
                ->join('acs_users', 'acs_sales.acs_users_id', '=', 'acs_users.id')
                ->leftJoin('acs_comission_settings', 'acs_sales.variety_type', '=', 'acs_comission_settings.variety_type')
                ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
                ->where('acs_sales.status', 0) // Only unprocessed sales
                ->whereDate('acs_sales.created_at', '<=', $this->lastCalculateDate) // Only up to selected date
                ->select(
                    'acs_sales.acs_users_id',
                    'acs_users.name as user_name',
                    'acs_users.email as user_email',
                    'acs_users.acs_coorperative_id',
                    'acs_coorperative.name as cooperative_name',
                    DB::raw('COUNT(*) as quantity'),
                    DB::raw('SUM(acs_sales.total_price) as total_price'),
                    'acs_sales.variety_type',
                    'acs_comission_settings.variety_name'
                )
                ->groupBy('acs_sales.acs_users_id', 'acs_users.name', 'acs_users.email', 'acs_users.acs_coorperative_id', 'acs_coorperative.name', 'acs_sales.variety_type', 'acs_comission_settings.variety_name')
                ->get();

        // If no unprocessed sales found for the selected date range, return early
        if ($userSales->isEmpty()) {
            return;
        }

        $totalMainAgentPayout = 0;
        $totalAgentPayout = 0;
        $cooperativeBreakdown = [];

        foreach($userSales as $userSale){
            // Find the commission tier for this user's total quantity
            $tierCommission = DB::table('acs_commission_tiers')
                    ->join('acs_comission_settings', 'acs_commission_tiers.commission_setting_id', '=', 'acs_comission_settings.id')
                    ->where('acs_comission_settings.variety_type', $userSale->variety_type)
                    ->where('acs_commission_tiers.min_qty', '<=', $userSale->quantity)
                    ->where('acs_commission_tiers.max_qty', '>=', $userSale->quantity)
                    ->whereNull('acs_commission_tiers.deleted_at')
                    ->select(
                        'acs_commission_tiers.commission_percentage',
                        'acs_commission_tiers.min_qty',
                        'acs_commission_tiers.max_qty',
                        'acs_comission_settings.variety_type'
                    )
                    ->first();

            // Calculate commission amount
            $commissionAmount = 0;
            $commissionPercentage = 0;

            if ($tierCommission) {
                $commissionPercentage = $tierCommission->commission_percentage;
                $commissionAmount = ($userSale->total_price * $commissionPercentage) / 100;
            }

            // Get commission split settings from database
            $commissionSplitSettings = DB::table('acs_comission_split_settings')
                ->where('default', true)
                ->whereNull('deleted_at')
                ->first();

            // Use default values if no settings found
            $mainAgentPercentage = $commissionSplitSettings ? $commissionSplitSettings->main_agent_percentage : 40;
            $agentPercentage = $commissionSplitSettings ? $commissionSplitSettings->agent_percentage : 60;

            $agentCommission = number_format($agentPercentage / 100 * $commissionAmount, 2, '.', '');
            $masterAgentCommission = number_format($mainAgentPercentage / 100 * $commissionAmount, 2, '.', '');

            // Accumulate by cooperative
            $cooperativeId = $userSale->acs_coorperative_id;
            $cooperativeName = $userSale->cooperative_name ?? 'Coop ID: ' . $cooperativeId;

            if (!isset($cooperativeBreakdown[$cooperativeId])) {
                $cooperativeBreakdown[$cooperativeId] = [
                    'name' => $cooperativeName,
                    'main_agent_commission' => 0,
                    'agent_commission' => 0,
                    'total_commission' => 0,
                    'sales_count' => 0,
                    'sales_value' => 0
                ];
            }

            $cooperativeBreakdown[$cooperativeId]['main_agent_commission'] += floatval($masterAgentCommission);
            $cooperativeBreakdown[$cooperativeId]['agent_commission'] += floatval($agentCommission);
            $cooperativeBreakdown[$cooperativeId]['total_commission'] += floatval($masterAgentCommission) + floatval($agentCommission);
            $cooperativeBreakdown[$cooperativeId]['sales_count'] += $userSale->quantity;
            $cooperativeBreakdown[$cooperativeId]['sales_value'] += $userSale->total_price;

            // Accumulate payout totals
            $totalMainAgentPayout += floatval($masterAgentCommission);
            $totalAgentPayout += floatval($agentCommission);
        }

        $this->previewTotalMainAgent = $totalMainAgentPayout;
        $this->previewTotalAgent = $totalAgentPayout;
        $this->cooperativeBreakdown = array_values($cooperativeBreakdown); // Convert to indexed array for easier iteration
    }

        public function processGeneration()
    {
        // Validate date
        if (!$this->lastCalculateDate) {
            session()->flash('error', 'Please select a last calculate date.');
            return;
        }

        // Get aggregated sales data grouped by user (only status = 0 and up to selected date)
        $userSales = DB::table('acs_sales')
                ->join('acs_users', 'acs_sales.acs_users_id', '=', 'acs_users.id')
                ->leftJoin('acs_comission_settings', 'acs_sales.variety_type', '=', 'acs_comission_settings.variety_type')
                ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
                ->where('acs_sales.status', 0) // Only unprocessed sales
                ->whereDate('acs_sales.created_at', '<=', $this->lastCalculateDate) // Only up to selected date
                ->select(
                    'acs_sales.acs_users_id',
                    'acs_users.name as user_name',
                    'acs_users.email as user_email',
                    'acs_users.acs_coorperative_id',
                    'acs_coorperative.name as cooperative_name',
                    DB::raw('COUNT(*) as quantity'),
                    DB::raw('SUM(acs_sales.total_price) as total_price'),
                    'acs_sales.variety_type',
                    'acs_comission_settings.variety_name'
                )
                ->groupBy('acs_sales.acs_users_id', 'acs_users.name', 'acs_users.email', 'acs_users.acs_coorperative_id', 'acs_coorperative.name', 'acs_sales.variety_type', 'acs_comission_settings.variety_name')
                ->get();

        if ($userSales->isEmpty()) {
            // Check if there are any unprocessed sales at all
            $totalUnprocessed = DB::table('acs_sales')->where('status', 0)->count();

            if ($totalUnprocessed === 0) {
                session()->flash('error', 'No unprocessed sales found in the system.');
            } else {
                session()->flash('error', "No unprocessed sales found for the selected date ({$this->lastCalculateDate}). Try selecting a later date or check if sales exist after this date.");
            }

            $this->showGenerateModal = false;
            return;
        }

        $totalMainAgentPayout = 0;
        $totalAgentPayout = 0;

        foreach($userSales as $userSale){
            // Find the commission tier for this user's total quantity
            $tierCommission = DB::table('acs_commission_tiers')
                    ->join('acs_comission_settings', 'acs_commission_tiers.commission_setting_id', '=', 'acs_comission_settings.id')
                    ->where('acs_comission_settings.variety_type', $userSale->variety_type)
                    ->where('acs_commission_tiers.min_qty', '<=', $userSale->quantity)
                    ->where('acs_commission_tiers.max_qty', '>=', $userSale->quantity)
                    ->whereNull('acs_commission_tiers.deleted_at')
                    ->select(
                        'acs_commission_tiers.commission_percentage',
                        'acs_commission_tiers.min_qty',
                        'acs_commission_tiers.max_qty',
                        'acs_comission_settings.variety_type'
                    )
                    ->first();

            // Calculate commission amount
            $commissionAmount = 0;
            $commissionPercentage = 0;

            if ($tierCommission) {
                $commissionPercentage = $tierCommission->commission_percentage;
                $commissionAmount = ($userSale->total_price * $commissionPercentage) / 100;
            }

            // Get commission split settings from database
            $commissionSplitSettings = DB::table('acs_comission_split_settings')
                ->where('default', true)
                ->whereNull('deleted_at')
                ->first();

            // Use default values if no settings found
            $mainAgentPercentage = $commissionSplitSettings ? $commissionSplitSettings->main_agent_percentage : 40;
            $agentPercentage = $commissionSplitSettings ? $commissionSplitSettings->agent_percentage : 60;

            $agentCommission = number_format($agentPercentage / 100 * $commissionAmount, 2, '.', '');
            $masterAgentCommission = number_format($mainAgentPercentage / 100 * $commissionAmount, 2, '.', '');

            // Save to database if commission found
            if ($tierCommission && $commissionAmount > 0) {
                DB::beginTransaction();
                try {
                    // Create sales commission record
                    $salesCommission = AcsSalesCommission::create([
                        'table_22_jualan_id' => 1, // You may need to adjust this
                        'table_23_senarai_jualan_id' => 1, // You may need to adjust this
                        'invoice_no' => 'INV-' . time() . '-' . $userSale->acs_users_id,
                        'affiliate_membership_no' => $userSale->acs_users_id,
                        'senarai_pelanggan_id' => 1, // You may need to adjust this
                        'comission_percentage' => $commissionPercentage,
                        'comission_amount' => $commissionAmount,
                    ]);

                    // Get user details for commission distribution
                    $userDetails = DB::table('acs_users')
                        ->where('id', $userSale->acs_users_id)
                        ->first();

                    // Create commission distribution record
                    AcsCommissionDistribution::create([
                        'acs_sales_commission_id' => $salesCommission->id,
                        'acs_coorperative_id' => $userSale->acs_coorperative_id,
                        'acs_coorperative_branch_id' => $userDetails->acs_coorperative_branch_id ?? 1,
                        'acs_main_agent_user_id' => $userDetails->invited_by ?? $userSale->acs_users_id,
                        'main_agent_percentage' => $mainAgentPercentage,
                        'main_agent_commission_amount' => $masterAgentCommission,
                        'agent_percentage' => $agentPercentage,
                        'agent_commission_amount' => $agentCommission,
                        'acs_agent_user_id' => $userSale->acs_users_id,
                    ]);

                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollback();
                    // Log error for debugging
                    Log::error('Commission distribution save failed: ' . $e->getMessage());
                }
            }

            // Accumulate payout totals
            $totalMainAgentPayout += floatval($masterAgentCommission);
            $totalAgentPayout += floatval($agentCommission);
        }

        // Update sales status to 1 only for sales up to the selected date
        $updatedCount = DB::table('acs_sales')
            ->where('status', 0)
            ->whereDate('created_at', '<=', $this->lastCalculateDate)
            ->update(['status' => 1]);

        // Check if there are still unprocessed sales remaining
        $remainingUnprocessedSales = DB::table('acs_sales')->where('status', 0)->count();
        $this->isSubmitted = ($remainingUnprocessedSales === 0);

        // Hide modal
        $this->showGenerateModal = false;

        session()->flash('success', "Commission report generated successfully! Processed {$updatedCount} sales up to {$this->lastCalculateDate}. " .
                         ($remainingUnprocessedSales > 0 ? "{$remainingUnprocessedSales} unprocessed sales remain." : "All sales have been processed."));
    }

    public function closeGenerateModal()
    {
        $this->showGenerateModal = false;
        $this->lastCalculateDate = '';
        $this->previewTotalMainAgent = 0;
        $this->previewTotalAgent = 0;
        $this->cooperativeBreakdown = [];
    }

    public function refreshStatus()
    {
        // Refresh the submission status by checking for unprocessed sales
        $this->isSubmitted = !DB::table('acs_sales')->where('status', 0)->exists();

        // Reset any cached data
        $this->resetPage();

        session()->flash('info', 'Status refreshed. ' .
                         ($this->isSubmitted ? 'No unprocessed sales found.' : 'Unprocessed sales are available for processing.'));
    }



    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
        $this->resetPage();
    }

    public function updatedSearchTerm()
    {
        $this->resetPage();
    }

    public function updatedLastCalculateDate()
    {
        $this->calculatePreviewTotals();
    }

    public function updatedDateTo()
    {
        $this->resetPage();
    }

    public function updatedProcessedDateTo()
    {
        $this->resetPage();
    }

    public function getCommissionHistoryProperty()
    {
        $query = DB::table('acs_sales')
            ->join('acs_users', 'acs_sales.acs_users_id', '=', 'acs_users.id')
            ->leftJoin('acs_comission_settings', 'acs_sales.variety_type', '=', 'acs_comission_settings.variety_type')
            ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
            ->where('acs_sales.status', 1) // Only processed sales
            ->select(
                'acs_sales.acs_users_id',
                'acs_users.name as user_name',
                'acs_users.email as user_email',
                'acs_users.acs_coorperative_id',
                'acs_coorperative.name as cooperative_name',
                'acs_sales.variety_type',
                'acs_comission_settings.variety_name',
                DB::raw('COUNT(*) as quantity'),
                DB::raw('SUM(acs_sales.total_price) as total_price'),
                DB::raw('GROUP_CONCAT(DISTINCT acs_sales.sku_number SEPARATOR ", ") as sku_numbers'),
                DB::raw('MIN(acs_sales.created_at) as first_sale_date'),
                DB::raw('MAX(acs_sales.created_at) as last_sale_date'),
                DB::raw('MAX(acs_sales.updated_at) as processed_date')
            )
            ->groupBy(
                'acs_sales.acs_users_id',
                'acs_users.name',
                'acs_users.email',
                'acs_users.acs_coorperative_id',
                'acs_coorperative.name',
                'acs_sales.variety_type',
                'acs_comission_settings.variety_name'
            )
            ->orderBy('last_sale_date', 'desc');

        // Apply search filter
        if ($this->searchTerm) {
            $query->where(function($q) {
                $q->where('acs_users.name', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_users.email', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_sales.variety_type', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_comission_settings.variety_name', 'like', '%' . $this->searchTerm . '%');
            });
        }

        // Apply date filter (created date)
        if ($this->dateTo) {
            $query->whereDate('acs_sales.created_at', '<=', $this->dateTo);
        }

        // Apply processed date filter
        if ($this->processedDateTo) {
            $query->whereDate('acs_sales.updated_at', '<=', $this->processedDateTo);
        }

        return $query->paginate(15);
    }

    public function getHistoryStatsProperty()
    {
        // Base query for processed sales
        $processedSalesQuery = DB::table('acs_sales')->where('status', 1);
        $totalValueQuery = DB::table('acs_sales')->where('status', 1);

        // Apply date filters if set
        if ($this->dateTo) {
            $processedSalesQuery->whereDate('created_at', '<=', $this->dateTo);
            $totalValueQuery->whereDate('created_at', '<=', $this->dateTo);
        }

        if ($this->processedDateTo) {
            $processedSalesQuery->whereDate('updated_at', '<=', $this->processedDateTo);
            $totalValueQuery->whereDate('updated_at', '<=', $this->processedDateTo);
        }

        $processedSales = $processedSalesQuery->count();
        $totalValue = $totalValueQuery->sum('total_price');

        // Get commission data from sales commission table
        $commissionStats = AcsSalesCommission::selectRaw('
            COUNT(*) as total_records,
            SUM(comission_amount) as total_commission,
            AVG(comission_amount) as avg_commission,
            MAX(comission_amount) as max_commission
        ')->first();

        $distributionStats = AcsCommissionDistribution::selectRaw('
            SUM(main_agent_commission_amount) as total_main_agent,
            SUM(agent_commission_amount) as total_agent
        ')->first();

        return [
            'total_records' => $commissionStats->total_records ?? 0,
            'total_commission' => $commissionStats->total_commission ?? 0,
            'avg_commission' => $commissionStats->avg_commission ?? 0,
            'max_commission' => $commissionStats->max_commission ?? 0,
            'total_main_agent' => $distributionStats->total_main_agent ?? 0,
            'total_agent' => $distributionStats->total_agent ?? 0,
            'processed_sales' => $processedSales,
            'total_value' => $totalValue,
        ];
    }

    public function getSalesListProperty()
    {
        $query = DB::table('acs_sales')
            ->join('acs_users', 'acs_sales.acs_users_id', '=', 'acs_users.id')
            ->leftJoin('acs_comission_settings', 'acs_sales.variety_type', '=', 'acs_comission_settings.variety_type')
            ->leftJoin('acs_coorperative', 'acs_users.acs_coorperative_id', '=', 'acs_coorperative.id')
            ->where('acs_sales.status', 0) // Only unprocessed sales
            ->select(
                'acs_sales.acs_users_id',
                'acs_users.name as user_name',
                'acs_users.email as user_email',
                'acs_users.acs_coorperative_id',
                'acs_coorperative.name as cooperative_name',
                'acs_sales.variety_type',
                'acs_comission_settings.variety_name',
                DB::raw('COUNT(*) as quantity'),
                DB::raw('SUM(acs_sales.total_price) as total_price'),
                DB::raw('GROUP_CONCAT(DISTINCT acs_sales.sku_number SEPARATOR ", ") as sku_numbers'),
                DB::raw('MIN(acs_sales.created_at) as first_sale_date'),
                DB::raw('MAX(acs_sales.created_at) as last_sale_date')
            )
            ->groupBy(
                'acs_sales.acs_users_id',
                'acs_users.name',
                'acs_users.email',
                'acs_users.acs_coorperative_id',
                'acs_coorperative.name',
                'acs_sales.variety_type',
                'acs_comission_settings.variety_name'
            )
            ->orderBy('last_sale_date', 'desc');

        // Apply search filter
        if ($this->searchTerm) {
            $query->where(function($q) {
                $q->where('acs_users.name', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_users.email', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_sales.variety_type', 'like', '%' . $this->searchTerm . '%')
                  ->orWhere('acs_comission_settings.variety_name', 'like', '%' . $this->searchTerm . '%');
            });
        }

        // Apply date filter
        if ($this->dateTo) {
            $query->whereDate('acs_sales.created_at', '<=', $this->dateTo);
        }

        return $query->paginate(15);
    }

    public function getSalesStatsProperty()
    {
        $totalSales = DB::table('acs_sales')->count();
        $processedSales = DB::table('acs_sales')->where('status', 1)->count();
        $unprocessedSales = DB::table('acs_sales')->where('status', 0)->count();
        $totalValue = DB::table('acs_sales')->sum('total_price');
        $unprocessedValue = DB::table('acs_sales')->where('status', 0)->sum('total_price');

        return [
            'total_sales' => $totalSales,
            'processed_sales' => $processedSales,
            'unprocessed_sales' => $unprocessedSales,
            'total_value' => $totalValue,
            'unprocessed_value' => $unprocessedValue,
        ];
    }

    public function calculateSaleCommission($sale)
    {
        // Use the quantity from the grouped data (already calculated)
        $quantity = $sale->quantity;

        // Find the commission tier
        $tierCommission = DB::table('acs_commission_tiers')
            ->join('acs_comission_settings', 'acs_commission_tiers.commission_setting_id', '=', 'acs_comission_settings.id')
            ->where('acs_comission_settings.variety_type', $sale->variety_type)
            ->where('acs_commission_tiers.min_qty', '<=', $quantity)
            ->where('acs_commission_tiers.max_qty', '>=', $quantity)
            ->where('acs_commission_tiers.deleted_at', null)
            ->select('acs_commission_tiers.commission_percentage')
            ->first();

        $commissionPercentage = $tierCommission ? $tierCommission->commission_percentage : 0;
        $commissionAmount = ($sale->total_price * $commissionPercentage) / 100;

        // Get commission split settings from database
        $commissionSplitSettings = DB::table('acs_comission_split_settings')
            ->where('default', true)
            ->whereNull('deleted_at')
            ->first();

        // Use default values if no settings found
        $mainAgentPercentage = $commissionSplitSettings ? $commissionSplitSettings->main_agent_percentage : 40;
        $agentPercentage = $commissionSplitSettings ? $commissionSplitSettings->agent_percentage : 60;

        $agentCommission = ($commissionAmount * $agentPercentage) / 100;
        $masterAgentCommission = ($commissionAmount * $mainAgentPercentage) / 100;

        return [
            'quantity_for_tier' => $quantity,
            'commission_percentage' => $commissionPercentage,
            'commission_amount' => $commissionAmount,
            'agent_commission' => $agentCommission,
            'master_agent_commission' => $masterAgentCommission,
            'tier_found' => $tierCommission ? true : false
        ];
    }


}
