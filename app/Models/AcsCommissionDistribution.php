<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUlids;

class AcsCommissionDistribution extends Model
{
    use HasFactory, SoftDeletes, HasUlids;

    protected $table = 'acs_commission_distributions';

    protected $fillable = [
        'acs_sales_commission_id',
        'acs_coorperative_id',
        'acs_coorperative_branch_id',
        'acs_main_agent_user_id',
        'main_agent_percentage',
        'main_agent_commission_amount',
        'agent_percentage',
        'agent_commission_amount',
        'acs_agent_user_id',
    ];

    protected $casts = [
        'acs_coorperative_id' => 'integer',
        'acs_coorperative_branch_id' => 'integer',
        'main_agent_percentage' => 'decimal:2',
        'main_agent_commission_amount' => 'decimal:2',
        'agent_percentage' => 'decimal:2',
        'agent_commission_amount' => 'decimal:2',
    ];

    /**
     * Get the sales commission this distribution belongs to
     */
    public function salesCommission(): BelongsTo
    {
        return $this->belongsTo(AcsSalesCommission::class, 'acs_sales_commission_id');
    }

    /**
     * Get the cooperative
     */
    public function cooperative(): BelongsTo
    {
        return $this->belongsTo(AcsCooperative::class, 'acs_coorperative_id');
    }

    /**
     * Get the cooperative branch
     */
    public function cooperativeBranch(): BelongsTo
    {
        return $this->belongsTo(AcsCooperativeBranch::class, 'acs_coorperative_branch_id');
    }

    /**
     * Get the main agent
     */
    public function mainAgent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'acs_main_agent_user_id');
    }

    /**
     * Get the agent
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'acs_agent_user_id');
    }

    /**
     * Get total commission amount
     */
    public function getTotalCommissionAmountAttribute(): float
    {
        return $this->main_agent_commission_amount + $this->agent_commission_amount;
    }

    /**
     * Get campaign information through sales commission
     */
    public function getCampaignAttribute()
    {
        return $this->salesCommission?->campaign;
    }
}
