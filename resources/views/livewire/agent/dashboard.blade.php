<div>
    <div class="row">
        <!-- Announcements Section - Left Column -->
        <div class="col-md-4">


            <!-- User Profile Card -->
            <div class="card user-card">
                <div class="card-body">
                    <div class="user-cover-bg">
                        <img src="{{asset('assets/images/application/img-user-cover-1.jpg')}}" alt="image" class="img-fluid">
                    </div>

                    <div class="chat-avtar card-user-image"><img src="{{asset('assets/images/user/avatar-1.jpg')}}" alt="user-image" class="img-thumbnail rounded-circle"> <i class="chat-badge bg-success"></i></div>
                    <div class="flex-wrap gap-2 d-flex">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ $userName }}</h6>
                            <p class="mb-0 text-sm text-muted">Registered Since {{\Carbon\Carbon::now()->format('d M Y')}}</p>
                        </div>
                    </div>

                    <div class="my-3 saprator"><span>Organization</span></div>
                    <div class="text-center">
                        <span class="mt-1 bg-transparent border badge bg-light-secondary rounded-pill border-secondary f-14 me-1">Organization Name</span>
                        <span class="mt-1 bg-transparent border badge bg-light-secondary rounded-pill border-secondary f-14 me-1">Branch/Outlet</span>
                    </div>

                    <div class="my-3 saprator"><span>Current Month Achievement</span></div>
                    <div class="my-3 text-center row g-3">
                        <div class="col-6">
                            <h5 class="mb-0">86</h5>
                            <small class="text-muted">Sales</small>
                        </div>
                        <div class="col-6">
                            <h5 class="mb-0">40</h5>
                            <small class="text-muted">Sales Amount</small>
                        </div>
                    </div>

                    <div class="my-3 saprator"><span>Monthly Sales Target</span></div>
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1 me-2">
                            <div class="progress" style="height: 8px">
                                <div class="progress-bar bg-primary" style="width: 60%"></div>
                            </div>
                        </div>
                        <div class="flex-shrink-0">
                            <h6 class="mb-0">30%</h6>
                        </div>
                    </div>
                    <div class="text-center">
                        <p class="mt-2">RM 200.00 / RM 2,000.00</p>
                        <button type="button" class="mt-1 btn btn-outline-primary btn-sm">Set target</button>
                    </div>
                </div>
            </div>

            @if($announcements->count() > 0)
                <div class="mb-4 card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="ph-duotone ph-megaphone text-primary me-2"></i>
                                Announcements
                            </h6>
                            <button wire:click="toggleAnnouncements" class="btn btn-sm btn-outline-primary">
                                <i class="ph-duotone {{ $showAnnouncements ? 'ph-eye-slash' : 'ph-eye' }}"></i>
                            </button>
                        </div>
                    </div>
                    @if($showAnnouncements)
                        <div class="p-0 card-body">
                            <div class="announcement-list">
                                @foreach($announcements->take(3) as $announcement)
                                    <div class="announcement-item border-bottom p-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                                        <div class="mb-2 d-flex justify-content-between align-items-start">
                                            <h6 class="mb-1 text-primary small">{{ $announcement->title }}</h6>
                                            <small class="text-muted">{{ $announcement->created_at->format('M d') }}</small>
                                        </div>
                                        <p class="mb-2 text-muted small" style="font-size: 0.8rem; line-height: 1.3;">
                                            {{ Str::limit($announcement->content, 80) }}
                                        </p>
                                        @if(strlen($announcement->content) > 80)
                                            <button class="p-0 btn btn-sm btn-link small"
                                                    type="button"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#announcementModal{{ $announcement->id }}">
                                                Read more...
                                            </button>
                                        @endif
                                    </div>

                                    <!-- Modal for full announcement content -->
                                    <div class="modal fade"
                                         id="announcementModal{{ $announcement->id }}"
                                         tabindex="-1"
                                         aria-labelledby="announcementModalLabel{{ $announcement->id }}"
                                         aria-hidden="true"
                                         wire:ignore.self>
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="announcementModalLabel{{ $announcement->id }}">
                                                        {{ $announcement->title }}
                                                    </h5>
                                                    <button type="button"
                                                            class="btn-close"
                                                            data-bs-dismiss="modal"
                                                            aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="mb-3">
                                                        <small class="text-muted">
                                                            <i class="ph-duotone ph-calendar"></i>
                                                            {{ $announcement->created_at->format('F d, Y \a\t H:i') }}
                                                        </small>
                                                    </div>
                                                    <div class="announcement-content">
                                                        {!! nl2br(e($announcement->content)) !!}
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button"
                                                            class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Close</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                                @if($announcements->count() > 3)
                                    <div class="p-3 text-center">
                                        <small class="text-muted">
                                            Showing 3 of {{ $announcements->count() }} announcements
                                        </small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            @endif

            <!-- Bank Account Details -->
            <div class="mt-4 card">
                <div class="card-body">
                    <div class="mb-4 d-flex align-items-center justify-content-between">
                        <h6 class="mb-0">Bank Account Details</h6>
                    </div>
                    <div class="overflow-hidden card rounded-4" style="background-image: url({{asset('assets/images/widget/img-card-bg.svg')}}); background-size: cover">
                        <div class="card-body">
                            <div class="d-flex">
                                <div class="flex-grow-1 me-3">
                                    <p class="mb-0 text-sm text-white text-opacity-50">BANK NAME</p>
                                    <h6 class="text-white">{{ $bankDetail->bankName->bank_name ?? '-' }}</h6>
                                </div>
                                <div class="flex-shrink-0"><img src="{{asset('assets/images/widget/img-card-master.svg')}}" alt="img" class="img-fluid"></div>
                            </div>
                            <h5 class="my-3 text-white">
                                @if(isset($bankDetail->account_number))
                                    **** **** **** {{ substr($bankDetail->account_number, -4) }}
                                @else
                                    ----
                                @endif
                            </h5>
                            <div class="row">
                                <div class="col-auto">
                                    <p class="mb-0 text-sm text-white text-opacity-50">EXP</p>
                                    <h6 class="mb-0 text-white">-</h6>
                                </div>
                                <div class="col-auto">
                                    <p class="mb-0 text-sm text-white text-opacity-50">CVV</p>
                                    <h6 class="mb-0 text-white">-</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Content - Right Column -->
        <div class="col-md-8">
            <div class="row">
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-inline-flex align-items-center">
                                    <i class="ph-duotone ph-shopping-cart text-primary me-2 f-20"></i>
                                    <p class="mb-0 text-muted">Total Sales</p>
                                </div>
                                <h4 class="mb-0">3.15k</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-inline-flex align-items-center">
                                    <i class="ph-duotone ph-money text-primary me-2 f-20"></i>
                                    <p class="mb-0 text-muted">Total Paid Commission</p>
                                </div>
                                <h4 class="mb-0">3.15k</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-inline-flex align-items-center">
                                    <i class="ph-duotone ph-calendar-blank text-primary me-2 f-20"></i>
                                    <p class="mb-0 text-muted">Current Month Sales</p>
                                </div>
                                <h4 class="mb-0">3.15k</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-inline-flex align-items-center">
                                    <i class="ph-duotone ph-money text-primary me-2 f-20"></i>
                                    <p class="mb-0 text-muted">Current Month Sales Amount</p>
                                </div>
                                <h4 class="mb-0">3.15k</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div id="yearly-summary-chart"></div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>Today's Sales</h5>
                        </div>
                        <div class="card-body table-border-style">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Customer</th>
                                            <th>Product Name</th>
                                            <th class="text-end">Sales Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>Mark</td>
                                            <td>Product Name</td>
                                            <td class="text-end">RM 200.00</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{asset('assets/js/plugins/apexcharts.min.js')}}"></script>
    <script>

    function initAgentDashboardChart() {
        // Destroy existing chart if it exists to prevent memory leaks
        if (window.agentChart && window.agentChart.destroy) {
            window.agentChart.destroy();
        }

        // Check if element exists before creating chart
        const chartElement = document.querySelector("#yearly-summary-chart");
        if (!chartElement) return;

        const options = {
            chart: {
                height: 250,
                type: "bar",
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: "75%",
                    borderRadius: 2,
                    borderRadiusApplication: "end"
                }
            },
            legend: {
                show: true,
                position: "bottom"
            },
            dataLabels: {
                enabled: false
            },
            colors: ["#1DE9B6", "#0398F2"],
            stroke: {
                show: true,
                width: 1,
                colors: ["transparent"]
            },
            fill: {
                type: "gradient",
                gradient: {
                    type: "vertical",
                    stops: [0, 100],
                    shadeIntensity: 0.5,
                    gradientToColors: ["#1DC4E9", "#38B9E7"]
                }
            },
            grid: {
                strokeDashArray: 4
            },
            series: [{
                name: "Sales",
                data: [76, 85, 101, 98, 87]
            }, {
                name: "Commission",
                data: [44, 55, 57, 56, 61]
            }],
            xaxis: {
                categories: ["Feb", "Mar", "Apr", "May", "Jun"]
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return "RM " + val;
                    }
                }
            }
        };

        window.agentChart = new ApexCharts(chartElement, options);
        window.agentChart.render();
    }

    // Initialize chart with delay to ensure DOM is ready
    function initAgentChartWithDelay() {
        setTimeout(initAgentDashboardChart, 100);
    }

    // Multiple event listeners to ensure chart loads after login and navigation
    document.addEventListener("DOMContentLoaded", initAgentChartWithDelay);
    document.addEventListener("livewire:navigated", initAgentChartWithDelay);
    document.addEventListener("livewire:load", initAgentChartWithDelay);
    document.addEventListener("turbo:load", initAgentChartWithDelay);

    // Immediate execution if DOM is already ready (for cases after login)
    if (document.readyState !== "loading") {
        initAgentChartWithDelay();
    }

    </script>

    <style>
        .announcement-item:last-child {
            border-bottom: none !important;
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        .announcement-content {
            white-space: pre-wrap;
            line-height: 1.6;
        }

        /* Ensure proper modal fade behavior */
        .modal.fade .modal-dialog {
            transition: transform 0.3s ease-out;
        }

        .modal.show .modal-dialog {
            transform: none;
        }

        /* Compact announcement styling */
        .announcement-list .card-body {
            max-height: 400px;
            overflow-y: auto;
        }

        .announcement-item {
            transition: background-color 0.2s ease;
        }

        .announcement-item:hover {
            background-color: #f8f9fa;
        }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure proper modal dismissal
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.addEventListener('hidden.bs.modal', function () {
                // Clean up any potential issues
                this.classList.remove('show');
                document.body.classList.remove('modal-open');
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            });
        });
    });
    </script>
</div>
