<div>
    <div class="row">
        <!-- Announcements Section - Left Column -->
        <div class="col-md-4">

            <!-- Master Agent Profile Card -->
            <div class="card">
                <div class="card-body">
                    <div class="mb-3 text-center">
                        <div class="mb-3 avatar avatar-lg">
                            <img src="{{asset('assets/images/user/avatar-1.jpg')}}" alt="user-image" class="img-thumbnail rounded-circle">
                        </div>
                        <h5 class="mb-1">{{ Auth::user()->name }}</h5>
                        <p class="mb-0 text-muted">{{ Auth::user()->role->name }}</p>
                    </div>

                    <div class="text-center row">
                        <div class="col-6">
                            <h6 class="mb-1">{{ $activeAgentsCount }}</h6>
                            <small class="text-muted">Active Agents</small>
                        </div>
                        <div class="col-6">
                            <h6 class="mb-1">{{ $activeBranchesCount }}</h6>
                            <small class="text-muted">Active Branches</small>
                        </div>
                    </div>
                </div>
            </div>

            @if($announcements->count() > 0)
                <div class="mb-4 card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <i class="ph-duotone ph-megaphone text-primary me-2"></i>
                                Announcements
                            </h6>
                            <button wire:click="toggleAnnouncements" class="btn btn-sm btn-outline-primary">
                                <i class="ph-duotone {{ $showAnnouncements ? 'ph-eye-slash' : 'ph-eye' }}"></i>
                            </button>
                        </div>
                    </div>
                    @if($showAnnouncements)
                        <div class="p-0 card-body">
                            <div class="announcement-list">
                                @foreach($announcements->take(3) as $announcement)
                                    <div class="announcement-item border-bottom p-3 {{ !$loop->last ? 'border-bottom' : '' }}">
                                        <div class="mb-2 d-flex justify-content-between align-items-start">
                                            <h6 class="mb-1 text-primary small">{{ $announcement->title }}</h6>
                                            <small class="text-muted">{{ $announcement->created_at->format('M d') }}</small>
                                        </div>
                                        <p class="mb-2 text-muted small" style="font-size: 0.8rem; line-height: 1.3;">
                                            {{ Str::limit($announcement->content, 80) }}
                                        </p>
                                        @if(strlen($announcement->content) > 80)
                                            <button class="p-0 btn btn-sm btn-link small"
                                                    type="button"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#announcementModal{{ $announcement->id }}">
                                                Read more...
                                            </button>
                                        @endif
                                    </div>

                                    <!-- Modal for full announcement content -->
                                    <div class="modal fade"
                                         id="announcementModal{{ $announcement->id }}"
                                         tabindex="-1"
                                         aria-labelledby="announcementModalLabel{{ $announcement->id }}"
                                         aria-hidden="true"
                                         wire:ignore.self>
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="announcementModalLabel{{ $announcement->id }}">
                                                        {{ $announcement->title }}
                                                    </h5>
                                                    <button type="button"
                                                            class="btn-close"
                                                            data-bs-dismiss="modal"
                                                            aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="mb-3">
                                                        <small class="text-muted">
                                                            <i class="ph-duotone ph-calendar"></i>
                                                            {{ $announcement->created_at->format('F d, Y \a\t H:i') }}
                                                        </small>
                                                    </div>
                                                    <div class="announcement-content">
                                                        {!! nl2br(e($announcement->content)) !!}
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button"
                                                            class="btn btn-secondary"
                                                            data-bs-dismiss="modal">Close</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                                @if($announcements->count() > 3)
                                    <div class="p-3 text-center">
                                        <small class="text-muted">
                                            Showing 3 of {{ $announcements->count() }} announcements
                                        </small>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            @endif

            <!-- Quick Stats -->
            <div class="mt-4 card">
                <div class="card-header">
                    <h6 class="mb-0">Quick Stats</h6>
                </div>
                <div class="card-body">
                    <div class="mb-2 d-flex justify-content-between">
                        <span>Total Sales</span>
                        <strong>{{ $totalSales }}</strong>
                    </div>
                    <div class="mb-2 d-flex justify-content-between">
                        <span>Commission</span>
                        <strong>RM {{ number_format($totalCommission, 2) }}</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>This Month</span>
                        <strong>RM {{ number_format($currentMonthSalesAmount, 2) }}</strong>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Content - Right Column -->
        <div class="col-md-8">
            <div class="mb-3 d-flex justify-content-end">
                <button wire:click="refreshDashboard" class="btn btn-sm btn-outline-primary">
                    <i class="ph-duotone ph-arrow-clockwise"></i> Refresh Data
                </button>
            </div>
            <div class="row">
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-inline-flex align-items-center">
                                    <i class="ph-duotone ph-shopping-cart text-primary me-2 f-20"></i>
                                    <p class="mb-0 text-muted">Total Sales</p>
                                </div>
                                <h4 class="mb-0">{{ $totalSales }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-inline-flex align-items-center">
                                    <i class="ph-duotone ph-money text-primary me-2 f-20"></i>
                                    <p class="mb-0 text-muted">Total Paid Commission</p>
                                </div>
                                <h4 class="mb-0">RM {{ number_format($totalCommission, 2) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-inline-flex align-items-center">
                                    <i class="ph-duotone ph-calendar-blank text-primary me-2 f-20"></i>
                                    <p class="mb-0 text-muted">Current Month Sales</p>
                                </div>
                                <h4 class="mb-0">{{ $currentMonthSales }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-6">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-inline-flex align-items-center">
                                    <i class="ph-duotone ph-money text-primary me-2 f-20"></i>
                                    <p class="mb-0 text-muted">Current Month Commission</p>
                                </div>
                                <h4 class="mb-0">RM {{ number_format($currentMonthSalesAmount, 2) }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div id="yearly-summary-chart"></div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>Today's Sales</h5>
                        </div>
                        <div class="card-body table-border-style">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>#</th>
                                            <th>Customer</th>
                                            <th>Product Name</th>
                                            <th class="text-end">Sales Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(count($todaySales) > 0)
                                            @foreach($todaySales as $index => $sale)
                                                <tr>
                                                    <td>{{ $index + 1 }}</td>
                                                    <td>{{ $sale['customer'] }}</td>
                                                    <td>{{ $sale['product_name'] }}</td>
                                                    <td class="text-end">RM {{ $sale['sales_amount'] }}</td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="4" class="text-center text-muted">No sales recorded today</td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{asset('assets/js/plugins/apexcharts.min.js')}}"></script>
    <script>

    function initMasterAgentDashboardChart() {
        // Destroy existing chart if it exists to prevent memory leaks
        if (window.masterAgentChart && window.masterAgentChart.destroy) {
            window.masterAgentChart.destroy();
        }

        // Check if element exists before creating chart
        const chartElement = document.querySelector("#yearly-summary-chart");
        if (!chartElement) return;

        const options = {
            chart: {
                height: 250,
                type: "bar",
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: "75%",
                    borderRadius: 2,
                    borderRadiusApplication: "end"
                }
            },
            legend: {
                show: true,
                position: "bottom"
            },
            dataLabels: {
                enabled: false
            },
            colors: ["#1DE9B6", "#0398F2"],
            stroke: {
                show: true,
                width: 1,
                colors: ["transparent"]
            },
            fill: {
                type: "gradient",
                gradient: {
                    type: "vertical",
                    stops: [0, 100],
                    shadeIntensity: 0.5,
                    gradientToColors: ["#1DC4E9", "#38B9E7"]
                }
            },
            grid: {
                strokeDashArray: 4
            },
            series: [{
                name: "Sales",
                data: {!! json_encode($chartData['sales'] ?? [0, 0, 0, 0, 0]) !!}
            }, {
                name: "Commission",
                data: {!! json_encode($chartData['commission'] ?? [0, 0, 0, 0, 0]) !!}
            }],
            xaxis: {
                categories: {!! json_encode($chartData['categories'] ?? ['Jan', 'Feb', 'Mar', 'Apr', 'May']) !!}
            },
            tooltip: {
                y: {
                    formatter: function(val) {
                        return "RM " + val;
                    }
                }
            }
        };

        window.masterAgentChart = new ApexCharts(chartElement, options);
        window.masterAgentChart.render();
    }

    // Initialize chart with delay to ensure DOM is ready
    function initMasterAgentChartWithDelay() {
        setTimeout(initMasterAgentDashboardChart, 100);
    }

    // Multiple event listeners to ensure chart loads after login and navigation
    document.addEventListener("DOMContentLoaded", initMasterAgentChartWithDelay);
    document.addEventListener("livewire:navigated", initMasterAgentChartWithDelay);
    document.addEventListener("livewire:load", initMasterAgentChartWithDelay);
    document.addEventListener("turbo:load", initMasterAgentChartWithDelay);

    // Immediate execution if DOM is already ready (for cases after login)
    if (document.readyState !== "loading") {
        initMasterAgentChartWithDelay();
    }

    </script>

    <style>
        .announcement-item:last-child {
            border-bottom: none !important;
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        .announcement-content {
            white-space: pre-wrap;
            line-height: 1.6;
        }

        /* Ensure proper modal fade behavior */
        .modal.fade .modal-dialog {
            transition: transform 0.3s ease-out;
        }

        .modal.show .modal-dialog {
            transform: none;
        }

        /* Compact announcement styling */
        .announcement-list .card-body {
            max-height: 400px;
            overflow-y: auto;
        }

        .announcement-item {
            transition: background-color 0.2s ease;
        }

        .announcement-item:hover {
            background-color: #f8f9fa;
        }
    </style>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure proper modal dismissal
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.addEventListener('hidden.bs.modal', function () {
                // Clean up any potential issues
                this.classList.remove('show');
                document.body.classList.remove('modal-open');
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            });
        });
    });
    </script>
</div>
