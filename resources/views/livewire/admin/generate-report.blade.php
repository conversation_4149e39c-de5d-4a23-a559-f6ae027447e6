<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Sales & Commission Management</h3>
                    <div class="card-tools">
                        <ul class="ml-auto nav nav-pills">
                            <li class="nav-item">
                                <a class="nav-link {{ $activeTab === 'sales' ? 'active' : '' }}"
                                   href="#" wire:click.prevent="setActiveTab('sales')">
                                    <i class="fas fa-clock me-2"></i>Unprocessed Sales
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ $activeTab === 'history' ? 'active' : '' }}"
                                   href="#" wire:click.prevent="setActiveTab('history')">
                                    <i class="fas fa-check-circle me-2"></i>Processed Sales
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    {{-- Flash Messages --}}
                    @if (session()->has('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if (session()->has('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif





                    {{-- Sales List Tab Content --}}
                    @if ($activeTab === 'sales')
                        {{-- Sales Statistics Cards --}}
                        <div class="mb-4 row">
                            <div class="col-md-3">
                                <div class="text-white card bg-info">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Sales</h5>
                                                <h3 class="mb-0">{{ $this->salesStats['total_sales'] }}</h3>
                                                <small class="opacity-75">All Records</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-shopping-cart fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-success">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Processed</h5>
                                                <h3 class="mb-0">{{ $this->salesStats['processed_sales'] }}</h3>
                                                <small class="opacity-75">Commission Generated</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-check-circle fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-warning">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Unprocessed</h5>
                                                <h3 class="mb-0">{{ $this->salesStats['unprocessed_sales'] }}</h3>
                                                <small class="opacity-75">Pending Commission</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-clock fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-primary">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Unprocessed Value</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->salesStats['unprocessed_value'], 2) }}</h3>
                                                <small class="opacity-75">Pending Sales</small>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-dollar-sign fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Search and Filter Section --}}
                        <div class="mb-4 card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="searchTerm" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="searchTerm"
                                               wire:model.live="searchTerm"
                                               placeholder="Search by user, variety name, variety type...">
                                    </div>
                                    <div class="col-md-4">
                                        <label for="dateTo" class="form-label">Date To</label>
                                        <input type="date" class="form-control" id="dateTo"
                                               wire:model.live="dateTo">
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button class="btn btn-secondary" wire:click="$set('searchTerm', ''); $set('dateTo', '')">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Sales List Table --}}
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Unprocessed Sales Records</h3>
                                <div class="card-tools">
                                    <span class="badge bg-warning">{{ $this->salesList->total() }} Unprocessed Records</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped table-sm">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>User</th>
                                                <th>Variety</th>
                                                <th>Quantity</th>
                                                <th>Total Price</th>
                                                <th>Commission %</th>
                                                <th>Commission Amount</th>
                                                <th>Main Agent (40%)</th>
                                                <th>Agent (60%)</th>
                                                <th>Date Range</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($this->salesList as $sale)
                                                @php
                                                    $commission = $this->calculateSaleCommission($sale);
                                                @endphp
                                                <tr>
                                                    <td>
                                                        <div class="small">
                                                            <strong>{{ $sale->user_name }}</strong><br>
                                                            <span class="text-muted">{{ $sale->user_email }}</span><br>
                                                            <small class="text-info">{{ $sale->cooperative_name ?? 'Coop ID: ' . $sale->acs_coorperative_id }}</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">{{ $sale->variety_name ?? $sale->variety_type }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-info">{{ $sale->quantity }}</span>
                                                    </td>
                                                    <td class="text-end">RM {{ number_format($sale->total_price, 2) }}</td>
                                                    <td class="text-center">{{ $commission['commission_percentage'] }}%</td>
                                                    <td class="text-end">RM {{ number_format($commission['commission_amount'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['master_agent_commission'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['agent_commission'], 2) }}</td>
                                                    <td class="small">
                                                        @if ($sale->first_sale_date == $sale->last_sale_date)
                                                            {{ \Carbon\Carbon::parse($sale->first_sale_date)->format('M d, Y') }}
                                                        @else
                                                            {{ \Carbon\Carbon::parse($sale->first_sale_date)->format('M d') }} -
                                                            {{ \Carbon\Carbon::parse($sale->last_sale_date)->format('M d, Y') }}
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        {{ $this->salesList->links() }}
                                    </div>
                                    @if ($this->salesStats['unprocessed_sales'] > 0 && !$isSubmitted)
                                        <div>
                                            <button wire:click="generate" class="btn btn-primary" wire:loading.attr="disabled">
                                                <span wire:loading.remove wire:target="generate">
                                                    <i class="fas fa-calculator me-2"></i>Generate Commission Report
                                                </span>
                                                <span wire:loading wire:target="generate">
                                                    <i class="fas fa-spinner fa-spin me-2"></i>Generating...
                                                </span>
                                            </button>
                                            <small class="mt-1 text-muted d-block">
                                                {{ $this->salesStats['unprocessed_sales'] }} unprocessed sales ready for commission generation
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endif

                    {{-- History Tab Content --}}
                    @if ($activeTab === 'history')
                        {{-- History Statistics Cards --}}
                        <div class="mb-4 row">
                            <div class="col-md-3">
                                <div class="text-white card bg-info">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Processed Sales</h5>
                                                <h3 class="mb-0">{{ $this->historyStats['processed_sales'] }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-check-circle fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-success">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Total Value</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->historyStats['total_value'], 2) }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-dollar-sign fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-primary">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Main Agent Total</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->historyStats['total_main_agent'], 2) }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-user-tie fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-white card bg-warning">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Agent Total</h5>
                                                <h3 class="mb-0">RM {{ number_format($this->historyStats['total_agent'], 2) }}</h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="opacity-50 fas fa-users fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Search and Filter Section --}}
                        <div class="mb-4 card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="searchTerm" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="searchTerm"
                                               wire:model.live="searchTerm"
                                               placeholder="Search by user, variety name, variety type...">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="dateTo" class="form-label">Created Date To</label>
                                        <input type="date" class="form-control" id="dateTo"
                                               wire:model.live="dateTo">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="processedDateTo" class="form-label">Processed Date To</label>
                                        <input type="date" class="form-control" id="processedDateTo"
                                               wire:model.live="processedDateTo">
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button class="btn btn-secondary" wire:click="$set('searchTerm', ''); $set('dateTo', ''); $set('processedDateTo', '')">
                                            <i class="fas fa-times me-1"></i>Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Processed Sales History Table --}}
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Processed Sales History</h3>
                                <div class="card-tools">
                                    <span class="badge bg-success">{{ $this->commissionHistory->total() }} Processed Records</span>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped table-sm">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>User</th>
                                                <th>Variety</th>
                                                <th>Quantity</th>
                                                <th>Total Price</th>
                                                <th>Commission %</th>
                                                <th>Commission Amount</th>
                                                <th>Main Agent (40%)</th>
                                                <th>Agent (60%)</th>
                                                <th>Processed Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($this->commissionHistory as $sale)
                                                @php
                                                    $commission = $this->calculateSaleCommission($sale);
                                                @endphp
                                                <tr>
                                                    <td>
                                                        <div class="small">
                                                            <strong>{{ $sale->user_name }}</strong><br>
                                                            <span class="text-muted">{{ $sale->user_email }}</span><br>
                                                            <small class="text-info">{{ $sale->cooperative_name ?? 'Coop ID: ' . $sale->acs_coorperative_id }}</small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">{{ $sale->variety_name ?? $sale->variety_type }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-success">{{ $sale->quantity }}</span>
                                                    </td>
                                                    <td class="text-end">RM {{ number_format($sale->total_price, 2) }}</td>
                                                    <td class="text-center">{{ $commission['commission_percentage'] }}%</td>
                                                    <td class="text-end">RM {{ number_format($commission['commission_amount'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['master_agent_commission'], 2) }}</td>
                                                    <td class="text-end">RM {{ number_format($commission['agent_commission'], 2) }}</td>
                                                    <td class="small">
                                                        @if ($sale->processed_date)
                                                            {{ \Carbon\Carbon::parse($sale->processed_date)->format('M d, Y H:i') }}
                                                        @else
                                                            {{ \Carbon\Carbon::parse($sale->last_sale_date)->format('M d, Y') }}
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="card-footer">
                                {{ $this->commissionHistory->links() }}
                            </div>
                        </div>
                    @endif {{-- End History Tab --}}
                </div>
            </div>
        </div>
    </div>

        {{-- Generate Commission Modal --}}
    @if ($showGenerateModal)
        <div class="modal fade show" tabindex="-1" style="display: block; background-color: rgba(0,0,0,0.5);"
             wire:ignore.self wire:click="closeGenerateModal">
            <div class="modal-dialog modal-lg" wire:click.stop>
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-calculator me-2"></i>Generate Commission Report
                        </h5>
                        <button type="button" class="btn-close" wire:click="closeGenerateModal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="lastCalculateDate" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>Last Calculate Date
                                    </label>
                                    <input type="date" class="form-control" id="lastCalculateDate"
                                           wire:model.live="lastCalculateDate" required>
                                    <small class="text-muted">Commission will be calculated for sales up to this date only</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-header">
                                        <h6 class="mb-0">
                                            <i class="fas fa-eye me-1"></i>Commission Preview by Cooperative
                                        </h6>
                                    </div>
                                    <div class="p-3 card-body" style="max-height: 400px; overflow-y: auto;">
                                        @if (!empty($cooperativeBreakdown))
                                            @foreach ($cooperativeBreakdown as $cooperative)
                                                <div class="p-2 mb-3 rounded border">
                                                    <div class="row align-items-center">
                                                        <div class="col-12">
                                                            <h6 class="mb-1 text-primary">
                                                                <i class="fas fa-building me-1"></i>{{ $cooperative['name'] }}
                                                            </h6>
                                                            <small class="text-muted">
                                                                {{ $cooperative['sales_count'] }} sales • RM {{ number_format($cooperative['sales_value'], 2) }}
                                                            </small>
                                                        </div>
                                                    </div>
                                                    <div class="mt-2 row">
                                                        <div class="col-6">
                                                            <small class="text-muted">Main Agent (40%)</small>
                                                            <div class="fw-bold text-primary">
                                                                RM {{ number_format($cooperative['main_agent_commission'], 2) }}
                                                            </div>
                                                        </div>
                                                        <div class="col-6">
                                                            <small class="text-muted">Agent (60%)</small>
                                                            <div class="fw-bold text-success">
                                                                RM {{ number_format($cooperative['agent_commission'], 2) }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach

                                            <hr class="my-3">
                                            <div class="p-2 text-center bg-white rounded">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <small class="text-muted">Total Main Agent</small>
                                                        <h5 class="mb-0 text-primary">
                                                            RM {{ number_format($previewTotalMainAgent, 2) }}
                                                        </h5>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">Total Agent</small>
                                                        <h5 class="mb-0 text-success">
                                                            RM {{ number_format($previewTotalAgent, 2) }}
                                                        </h5>
                                                    </div>
                                                </div>
                                                <hr class="my-2">
                                                <h6 class="mb-1 text-muted">Grand Total Commission</h6>
                                                <h4 class="mb-0 text-dark">
                                                    RM {{ number_format($previewTotalMainAgent + $previewTotalAgent, 2) }}
                                                </h4>
                                            </div>
                                        @else
                                            <div class="py-3 text-center">
                                                <i class="mb-2 fas fa-info-circle text-muted fa-2x"></i>
                                                <p class="mb-0 text-muted">No commission data to preview</p>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if (!$lastCalculateDate)
                            <div class="alert alert-warning" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Please select a last calculate date to see the preview.
                            </div>
                        @elseif (empty($cooperativeBreakdown))
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle me-2"></i>
                                No unprocessed sales found for the selected date.
                            </div>
                        @endif
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" wire:click="closeGenerateModal">
                            <i class="fas fa-times me-1"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-primary"
                                wire:click="processGeneration"
                                wire:loading.attr="disabled"
                                @if (!$lastCalculateDate || empty($cooperativeBreakdown)) disabled @endif>
                            <span wire:loading.remove wire:target="processGeneration">
                                <i class="fas fa-check me-2"></i>Generate Commission
                            </span>
                            <span wire:loading wire:target="processGeneration">
                                <i class="fas fa-spinner fa-spin me-2"></i>Processing...
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
