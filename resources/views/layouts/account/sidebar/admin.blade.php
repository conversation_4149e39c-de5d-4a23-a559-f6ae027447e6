@php
    $currentPath = request()->path();
    $pendingBranchCount = \App\Models\AcsCooperativeBranch::where('status', 'pending')->whereNull('deleted_at')->count();
@endphp

<!-- Dashboard -->
<li class="pc-item pc-caption">
    <label data-i18n="Main">Main</label>
    <i class="ph-duotone ph-devices"></i>
</li>
<li class="pc-item {{ $currentPath == 'admin/dashboard' ? 'active' : '' }}">
    <a href="{{url('admin/dashboard')}}" class="pc-link">
        <span class="pc-micon"><i class="ph-duotone ph-house"></i></span>
        <span class="pc-mtext" data-i18n="Dashboard">Dashboard</span>
    </a>
</li>

<!-- User Management -->
<li class="pc-item pc-caption">
    <label data-i18n="User Management">User Management</label>
    <i class="ph-duotone ph-users"></i>
</li>
<li class="pc-item pc-hasmenu {{ in_array($currentPath, ['admin/master-agent', 'admin/agent-list', 'admin/list-admin']) ? 'active' : '' }}">
    <a href="#!" class="pc-link">
        <span class="pc-micon"><i class="ph-duotone ph-users"></i></span>
        <span class="pc-mtext" data-i18n="User Management">User Management</span>
        <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
    </a>
    <ul class="pc-submenu">
        <li class="pc-item {{ $currentPath == 'admin/master-agent' ? 'active' : '' }}">
            <a href="{{url('admin/master-agent')}}" class="pc-link" data-i18n="Main Agent">Main Agent</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/agent-list' ? 'active' : '' }}">
            <a href="{{url('admin/agent-list')}}" class="pc-link" data-i18n="Agent">Agent</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/list-admin' ? 'active' : '' }}">
            <a href="{{route('admin.list-admin')}}" class="pc-link" data-i18n="Admin List">Admin List</a>
        </li>
    </ul>
</li>

<!-- Organization Management -->
<li class="pc-item pc-caption">
    <label data-i18n="Organization">Organization</label>
    <i class="ph-duotone ph-buildings"></i>
</li>
<li class="pc-item pc-hasmenu {{ in_array($currentPath, ['admin/cooperative', 'admin/branch', 'admin/organization-hierarchy']) ? 'active' : '' }}">
    <a href="#!" class="pc-link">
        <span class="pc-micon"><i class="ph-duotone ph-buildings"></i></span>
        <span class="pc-mtext" data-i18n="Organization">Organization</span>
        <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
    </a>
    <ul class="pc-submenu">
        <li class="pc-item {{ str_contains($currentPath, 'admin/cooperative') ? 'active' : '' }}">
            <a href="{{route('admin.cooperatives')}}" class="pc-link" data-i18n="Cooperative List">Cooperative List</a>
        </li>
        <li class="pc-item {{ str_contains($currentPath, 'admin/branch') ? 'active' : '' }}">
            <a href="{{route('admin.branch.index')}}" class="pc-link" data-i18n="Branch List">Branch List
                @if($pendingBranchCount > 0)
                    <span class="badge bg-warning ms-2">{{ $pendingBranchCount > 99 ? '99+' : $pendingBranchCount }}</span>
                @endif
            </a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/organization-hierarchy' ? 'active' : '' }}">
            <a href="{{route('admin.organization-hierarchy')}}" class="pc-link" data-i18n="Organization Hierarchy">Organization Hierarchy</a>
        </li>
    </ul>
</li>

<!-- Business Management -->
<li class="pc-item pc-caption">
    <label data-i18n="Business">Business</label>
    <i class="ph-duotone ph-briefcase"></i>
</li>
<li class="pc-item pc-hasmenu {{ in_array($currentPath, ['admin/commission', 'admin/sales-management', 'admin/announcements']) ? 'active' : '' }}">
    <a href="#!" class="pc-link">
        <span class="pc-micon"><i class="ph-duotone ph-briefcase"></i></span>
        <span class="pc-mtext" data-i18n="Business Management">Business Management</span>
        <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
    </a>
    <ul class="pc-submenu">
        <li class="pc-item {{ str_contains($currentPath, 'admin/commission') ? 'active' : '' }}">
            <a href="{{route('admin.commission.index')}}" class="pc-link" data-i18n="Commission Settings">Commission Settings</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/commission/pending-approvals' ? 'active' : '' }}">
            <a href="{{route('admin.commission.pending-approvals')}}" class="pc-link" data-i18n="Pending Approvals">Pending Approvals</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/sales-management' ? 'active' : '' }}">
            <a href="{{route('admin.sales-management')}}" class="pc-link" data-i18n="Sales Management">Sales Management</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/announcements' ? 'active' : '' }}">
            <a href="{{route('admin.announcements.index')}}" class="pc-link" data-i18n="Announcement Management">Announcement</a>
        </li>
    </ul>
</li>

<!-- Agreements & Legal -->
<li class="pc-item pc-caption">
    <label data-i18n="Legal">Legal</label>
    <i class="ph-duotone ph-file-text"></i>
</li>
<li class="pc-item {{ $currentPath == 'admin/agreement-list' ? 'active' : '' }}">
    <a href="{{route('admin.agreement-list')}}" class="pc-link">
        <span class="pc-micon"><i class="ph-duotone ph-file-text"></i></span>
        <span class="pc-mtext" data-i18n="Agreement List">Agreement List</span>
    </a>
</li>

<!-- Reports & Analytics -->
<li class="pc-item pc-caption">
    <label data-i18n="Reports">Reports & Analytics</label>
    <i class="ph-duotone ph-chart-bar"></i>
</li>
<li class="pc-item pc-hasmenu {{ in_array($currentPath, ['admin/commission/distribution-reports', 'admin/generate-report', 'admin/profit-simulation']) ? 'active' : '' }}">
    <a href="#!" class="pc-link">
        <span class="pc-micon"><i class="ph-duotone ph-chart-bar"></i></span>
        <span class="pc-mtext" data-i18n="Reports & Analytics">Reports & Analytics</span>
        <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
    </a>
    <ul class="pc-submenu">
        <li class="pc-item {{ $currentPath == 'admin/commission/distribution-reports' ? 'active' : '' }}">
            <a href="{{route('admin.commission.distribution-reports')}}" class="pc-link" data-i18n="Commission Distribution Reports">Commission Reports</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/generate-report' ? 'active' : '' }}">
            <a href="{{route('admin.generate-report')}}" class="pc-link" data-i18n="Generate Report">Generate Report</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/profit-simulation' ? 'active' : '' }}">
            <a href="{{url('admin/profit-simulation')}}" class="pc-link" data-i18n="Profit Simulation">Profit Simulation</a>
        </li>
    </ul>
</li>

<!-- System Management -->
<li class="pc-item pc-caption">
    <label data-i18n="System">System Management</label>
    <i class="ph-duotone ph-gear"></i>
</li>
<li class="pc-item pc-hasmenu {{ in_array($currentPath, ['admin/referral-code-management', 'admin/rms-gold-commerce-integration', 'admin/working-positions', 'admin/income-range-management', 'admin/email-templates']) ? 'active' : '' }}">
    <a href="#!" class="pc-link">
        <span class="pc-micon"><i class="ph-duotone ph-gear"></i></span>
        <span class="pc-mtext" data-i18n="System Management">System Management</span>
        <span class="pc-arrow"><i data-feather="chevron-right"></i></span>
    </a>
    <ul class="pc-submenu">
        <li class="pc-item {{ $currentPath == 'admin/email-templates' ? 'active' : '' }}">
            <a href="{{route('admin.email-templates')}}" class="pc-link" data-i18n="Email Templates">Email Templates</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/referral-code-management' ? 'active' : '' }}">
            <a href="{{route('admin.referral-code-management')}}" class="pc-link" data-i18n="Referral Code Management">Referral Code Management</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/rms-gold-commerce-integration' ? 'active' : '' }}">
            <a href="{{route('admin.rms-gold-commerce-integration')}}" class="pc-link" data-i18n="RMS Gold Commerce">RMS Gold Commerce</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/working-positions' ? 'active' : '' }}">
            <a href="{{url('admin/working-positions')}}" class="pc-link" data-i18n="Working Position Management">Working Position Management</a>
        </li>
        <li class="pc-item {{ $currentPath == 'admin/income-range-management' ? 'active' : '' }}">
            <a href="{{route('admin.income-range-management')}}" class="pc-link" data-i18n="Income Range Management">Income Range Management</a>
        </li>
    </ul>
</li>

<!-- Account Management -->
<li class="pc-item pc-caption">
    <label data-i18n="Account">Account</label>
    <i class="ph-duotone ph-user"></i>
</li>
<li class="pc-item {{ $currentPath == 'admin/change-password' ? 'active' : '' }}">
    <a href="{{route('admin.change-password')}}" class="pc-link">
        <span class="pc-micon"><i class="ph-duotone ph-lock-key"></i></span>
        <span class="pc-mtext" data-i18n="Change Password">Change Password</span>
    </a>
</li>
