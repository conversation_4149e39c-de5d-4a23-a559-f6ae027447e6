      <header class="pc-header">
         <div class="header-wrapper">
            <!-- [Mobile Media Block] start -->
            <div class="me-auto pc-mob-drp">
               <ul class="list-unstyled">
                  <!-- ======= Menu collapse Icon ===== -->
                  <li class="pc-h-item pc-sidebar-collapse"><a href="#" class="pc-head-link ms-0" id="sidebar-hide"><i class="ti ti-menu-2"></i></a></li>
                  <li class="pc-h-item pc-sidebar-popup"><a href="#" class="pc-head-link ms-0" id="mobile-collapse"><i class="ti ti-menu-2"></i></a></li>
               </ul>
            </div>
            <!-- [Mobile Media Block end] -->
            <div class="ms-auto">
               <ul class="list-unstyled">
                  @auth
                     @if(auth()->user()->hasAnyRole(['admin', 'super-admin']))
                        @livewire('admin.header-notifications')
                     @endif
                  @endauth
                  <li class="dropdown pc-h-item header-user-profile">
                     <a class="pc-head-link dropdown-toggle arrow-none me-0" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="false" data-bs-auto-close="outside" aria-expanded="false"><img src="{{asset('assets/images/user/avatar-2.jpg')}}" alt="user-image" class="user-avtar"></a>
                     <div class="dropdown-menu dropdown-user-profile dropdown-menu-end pc-h-dropdown">
                        <div class="dropdown-header d-flex align-items-center justify-content-between">
                           <h5 class="m-0">Profile</h5>
                        </div>
                        <div class="dropdown-body">
                           <div class="profile-notification-scroll position-relative" style="max-height: calc(100vh - 225px)">
                              <ul class="list-group list-group-flush w-100">
                                 <li class="list-group-item">
                                    <div class="d-flex align-items-center">
                                       <div class="flex-shrink-0"><img src="{{asset('assets/images/user/avatar-2.jpg')}}" alt="user-image" class="wid-50 rounded-circle"></div>
                                       <div class="mx-3 flex-grow-1">
                                        @auth
                                            <span class="badge bg-primary">{{ auth()->user()->role ? strtoupper(auth()->user()->role->name) : 'USER' }}</span>
                                        @endauth
                                          @auth
                                             <h5 class="mt-2 mb-0">{{ auth()->user()->name }}</h5>
                                             <a class="link-primary" href="mailto:{{ auth()->user()->email }}">{{ auth()->user()->email }}</a>
                                          @else
                                             <h5 class="mb-0">Guest User</h5>
                                             <a class="link-primary" href="#">Please login</a>
                                          @endauth
                                       </div>
                                    </div>
                                 </li>
                                 <li class="list-group-item">
                                    @auth
                                        @if(auth()->user()->hasAnyRole(['admin', 'super-admin']))
                                            <a href="{{ route('admin.change-password') }}" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-key"></i> <span>Change password</span> </span></a>
                                        @elseif(auth()->user()->hasRole('main-agent'))
                                            <a href="{{ route('master_agent.change-password') }}" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-key"></i> <span>Change password</span> </span></a>
                                        @elseif(auth()->user()->hasRole('agent'))
                                            <a href="{{ route('agent.change-password') }}" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-key"></i> <span>Change password</span> </span></a>
                                        @else
                                            <a href="#" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-key"></i> <span>Change password</span> </span></a>
                                        @endif
                                    @endauth
                                    {{-- <a href="#" class="dropdown-item">
                                       <span class="d-flex align-items-center"><i class="ph-duotone ph-envelope-simple"></i> <span>Recently mail</span></span>
                                       <div class="user-group"><img src="{{asset('assets/images/user/avatar-1.jpg')}}" alt="user-image" class="avtar"> <img src="{{asset('assets/images/user/avatar-2.jpg')}}" alt="user-image" class="avtar"> <img src="{{asset('assets/images/user/avatar-3.jpg')}}" alt="user-image" class="avtar"></div>
                                    </a>
                                    <a href="#" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-calendar-blank"></i> <span>Schedule meetings</span></span></a> --}}
                                 </li>
                                 {{-- <li class="list-group-item"><a href="#" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-heart"></i> <span>Favorite</span> </span></a><a href="#" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-arrow-circle-down"></i> <span>Download</span> </span><span class="text-white avtar avtar-xs rounded-circle bg-danger">10</span></a></li>
                                 <li class="list-group-item">
                                    <div class="dropdown-item">
                                       <span class="d-flex align-items-center"><i class="ph-duotone ph-globe-hemisphere-west"></i> <span>Languages</span> </span>
                                       <span class="flex-shrink-0">
                                          <select class="bg-transparent border-0 shadow-none form-select form-select-sm">
                                             <option value="1">English</option>
                                             <option value="2">Spain</option>
                                             <option value="3">Arbic</option>
                                          </select>
                                       </span>
                                    </div>
                                    <a href="#" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-flag"></i> <span>Country</span></span></a>
                                 </li> --}}
                                 {{-- <li class="list-group-item"><a href="#" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-user-circle"></i> <span>Edit profile</span> </span></a><a href="#" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-star text-warning"></i> <span>Upgrade account</span> <span class="border badge bg-light-success border-success ms-2">NEW</span> </span></a><a href="#" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-bell"></i> <span>Notifications</span> </span></a><a href="#" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-gear-six"></i> <span>Settings</span></span></a></li> --}}
                                 <li class="list-group-item">
                                    {{-- <a href="#" class="dropdown-item"><span class="d-flex align-items-center"><i class="ph-duotone ph-plus-circle"></i> <span>Add account</span> </span></a> --}}
                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                                       @csrf
                                    </form>
                                    <a href="#" class="dropdown-item" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                       <span class="d-flex align-items-center"><i class="ph-duotone ph-power"></i> <span>Logout</span></span>
                                    </a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                  </li>
               </ul>
            </div>
         </div>
      </header>
