      <nav class="pc-sidebar">
         <div class="navbar-wrapper">
            <div class="m-header">
               <a href="{{url('/')}}" class="b-brand text-primary">
                  <!-- ========   Change your logo from here   ============ -->
                  <h4 class="text-white">ACS RMS</h4>
                  <!-- <img src="{{asset('assets/images/logo-dark.svg')}}" alt="logo image" class="logo-lg">  -->
                  <!-- <span class="badge bg-brand-color-2 rounded-pill ms-1 theme-version">v1.3.0</span> -->
               </a>
            </div>
            <div class="navbar-content">
               <ul class="pc-navbar">
<!--                   <li class="pc-item pc-caption"><label data-i18n="Navigation">Navigation</label> <i class="ph-duotone ph-gauge"></i></li>
                  <li class="pc-item pc-hasmenu">
                     <a href="#!" class="pc-link"><span class="pc-micon"><i class="ph-duotone ph-gauge"></i> </span><span class="pc-mtext" data-i18n="Dashboard">Dashboard</span> <span class="pc-arrow"><i data-feather="chevron-right"></i></span> <span class="pc-badge">2</span></a>
                     <ul class="pc-submenu">
                        <li class="pc-item"><a class="pc-link" href="../dashboard/index.html" data-i18n="Analytics">Analytics</a></li>
                        <li class="pc-item"><a class="pc-link" href="../dashboard/affiliate.html" data-i18n="Affiliate">Affiliate</a></li>
                        <li class="pc-item"><a class="pc-link" href="../dashboard/finance.html" data-i18n="Finance">Finance</a></li>
                        <li class="pc-item"><a class="pc-link" href="../admins/helpdesk-dashboard.html" data-i18n="Helpdesk">Helpdesk</a></li>
                        <li class="pc-item"><a class="pc-link" href="../dashboard/invoice.html" data-i18n="Invoice">Invoice</a></li>
                     </ul>
                  </li> -->



                    @php
                        $path = request()->path();
                        $prefix = explode('/', $path)[0];
                    @endphp

                    @if($prefix == "super-admin")

                      @include('layouts.account.sidebar.super-admin')

                    @elseif($prefix == "admin")

                      @include('layouts.account.sidebar.admin')

                    @elseif($prefix == "main-agent")

                      @include('layouts.account.sidebar.master-agent')

                    @else

                      @include('layouts.account.sidebar.agent')

                    @endif



               </ul>
<!--                <div class="card nav-action-card bg-brand-color-4">
                  <div class="card-body" style="background-image: url('../assets/images/layout/nav-card-bg.svg')">
                     <h5 class="text-dark">Help Center</h5>
                     <p class="text-opacity-75 text-dark">Please contact us for more questions.</p>
                     <a href="https://phoenixcoded.support-hub.io/" class="btn btn-primary" target="_blank">Go to help Center</a>
                  </div>
               </div> -->
            </div>
            <div class="card pc-user-card">
               <div class="card-body">
                  <div class="d-flex align-items-center">
                                             <div class="flex-shrink-0"><img src="{{asset('assets/images/user/avatar-1.jpg')}}" alt="user-image" class="user-avtar wid-45 rounded-circle"></div>
                     <div class="flex-grow-1 ms-3">
                        <div class="dropdown">
                           <a href="#" class="arrow-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-offset="0,20">
                              <div class="d-flex align-items-center">
                                 <div class="flex-grow-1 me-2">
                                    <h6 class="mb-0">Username</h6>
                                    <small>Role</small>
                                 </div>
                                 <div class="flex-shrink-0">
                                    <div class="btn btn-icon btn-link-secondary avtar"><i class="ph-duotone ph-windows-logo"></i></div>
                                 </div>
                              </div>
                           </a>
                           <div class="dropdown-menu">
                              <ul>
                                 <li><a class="pc-user-links"><i class="ph-duotone ph-user"></i> <span>My Account</span></a></li>
                                 <li><a class="pc-user-links"><i class="ph-duotone ph-gear"></i> <span>Settings</span></a></li>
                                 <!-- <li><a class="pc-user-links"><i class="ph-duotone ph-lock-key"></i> <span>Lock Screen</span></a></li> -->
                                 <li>
                                    <form id="sidebar-logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                                       @csrf
                                    </form>
                                    <a class="pc-user-links" href="#" onclick="event.preventDefault(); document.getElementById('sidebar-logout-form').submit();">
                                       <i class="ph-duotone ph-power"></i> <span>Logout</span>
                                    </a>
                                 </li>
                              </ul>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </nav>
