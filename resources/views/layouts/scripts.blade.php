<script src="{{asset('assets/js/plugins/popper.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/simplebar.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/bootstrap.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/i18next.min.js')}}"></script>
<script src="{{asset('assets/js/plugins/i18nextHttpBackend.min.js')}}"></script>
<script src="{{asset('assets/js/script.js')}}"></script>
<script src="{{asset('assets/js/theme.js')}}"></script>
<script src="{{asset('assets/js/multi-lang.js')}}"></script>
<script src="{{asset('assets/js/plugins/feather.min.js')}}"></script>
<script>layout_change('light');</script>
<script>layout_sidebar_change('dark');</script>
<script>change_box_container('false');</script>
<script>layout_caption_change('true');</script>
<script>layout_rtl_change('false');</script>
<script>preset_change('preset-1');</script>

@stack('scripts')
<script>
   // Copy to clipboard functionality for Livewire
   window.addEventListener('copy-to-clipboard', event => {
      if (navigator.clipboard) {
         navigator.clipboard.writeText(event.detail).then(function() {
            // Simple alert for success
            alert('Link copied to clipboard!');
         }).catch(function() {
            // Fallback for older browsers
            alert('Link copied: ' + event.detail);
         });
      } else {
         // Fallback for older browsers
         alert('Link: ' + event.detail);
      }
   });

   // Handle redirect to login for password creation
   window.addEventListener('redirect-to-login', event => {
      setTimeout(() => {
         window.location.href = '/login';
      }, 3000);
   });

</script>
<!-- [Livewire Scripts] -->
@livewireScripts




